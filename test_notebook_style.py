#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试选项卡样式的简单程序
"""

import ttkbootstrap as ttk
from ttkbootstrap import Style

def create_test_window():
    """创建测试窗口"""
    # 创建主窗口
    window = ttk.Window(themename="cosmo")
    window.title("选项卡样式测试")
    window.geometry("800x600")
    
    # 配置样式
    style = Style()
    
    # 配置现代化的Notebook样式
    style.configure(
        'Modern.TNotebook',
        background='#f8f9fa',
        borderwidth=0,
        tabposition='n'  # 确保选项卡在顶部
    )
    
    style.configure(
        'Modern.TNotebook.Tab',
        padding=[60, 12],  # 增加水平内边距以实现更好的分布
        font=('Microsoft YaHei', 11, 'bold'),
        background='#e9ecef',
        foreground='#495057',
        expand=[1, 0, 0, 0],  # 让选项卡水平扩展以均匀分布
        anchor='center'  # 文本居中对齐
    )
    
    style.map(
        'Modern.TNotebook.Tab',
        background=[('selected', '#ffffff'), ('active', '#dee2e6')],
        foreground=[('selected', '#0d6efd'), ('active', '#495057')],
        expand=[('selected', [1, 0, 0, 0]), ('!selected', [1, 0, 0, 0])]  # 保持扩展状态
    )
    
    # 创建主容器
    main_container = ttk.Frame(window)
    main_container.pack(fill="both", expand=True, padx=20, pady=20)
    main_container.grid_rowconfigure(0, weight=1)
    main_container.grid_columnconfigure(0, weight=1)
    
    # 创建选项卡控件
    notebook = ttk.Notebook(main_container, style='Modern.TNotebook', width=800)
    notebook.grid(row=0, column=0, sticky="nsew")
    notebook.grid_columnconfigure(0, weight=1)

    # 创建三个选项卡
    tab1 = ttk.Frame(notebook)
    notebook.add(tab1, text="单任务")

    tab2 = ttk.Frame(notebook)
    notebook.add(tab2, text="多任务")

    tab3 = ttk.Frame(notebook)
    notebook.add(tab3, text="设置")

    # 配置选项卡以实现均匀分布
    configure_notebook_tabs(notebook, style)

    # 在每个选项卡中添加一些内容
    ttk.Label(tab1, text="这是单任务选项卡", font=('Microsoft YaHei', 14)).pack(expand=True)
    ttk.Label(tab2, text="这是多任务选项卡", font=('Microsoft YaHei', 14)).pack(expand=True)
    ttk.Label(tab3, text="这是设置选项卡", font=('Microsoft YaHei', 14)).pack(expand=True)

    return window

def configure_notebook_tabs(notebook, style):
    """配置选项卡样式以实现居中均匀分布"""
    try:
        # 获取选项卡的数量
        tab_count = notebook.index("end")

        # 计算每个选项卡的宽度（基于总宽度均匀分布）
        total_width = 2000  # Notebook的总宽度
        tab_width = total_width // tab_count

        # 更新样式以实现均匀分布
        style.configure(
            'Modern.TNotebook.Tab',
            padding=[tab_width//4, 12],  # 动态计算内边距
            font=('Microsoft YaHei', 12, 'bold'),
            background='#e9ecef',
            foreground='#495057',
            justify='center',
            anchor='center',
            width=tab_width//8  # 设置选项卡宽度
        )

        print(f"配置了 {tab_count} 个选项卡，每个宽度约 {tab_width} 像素")

    except Exception as e:
        print(f"配置选项卡样式时出错: {e}")

if __name__ == "__main__":
    window = create_test_window()
    window.mainloop()
