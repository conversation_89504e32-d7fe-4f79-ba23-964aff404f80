#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试文字宽度计算和显示效果
"""

import ttkbootstrap as ttk
from ttkbootstrap import Style
import tkinter.font as tkFont

class TextWidthTester:
    def __init__(self):
        self.window = ttk.Window(themename="cosmo")
        self.window.title("文字宽度测试器")
        self.window.geometry("1000x700")
        
        self.style = Style()
        self.create_interface()
        
        # 绑定事件
        self.window.bind('<Configure>', self.on_resize)
        
        # 初始测试
        self.window.after(100, self.test_text_width)
        
    def create_interface(self):
        """创建界面"""
        # 主容器
        main_frame = ttk.Frame(self.window)
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # 标题
        ttk.Label(
            main_frame,
            text="文字宽度测试器 - 解决文字遮挡问题",
            font=('Microsoft YaHei', 16, 'bold'),
            foreground='#0d6efd'
        ).pack(pady=(0, 10))
        
        # 控制面板
        control_frame = ttk.Frame(main_frame)
        control_frame.pack(fill="x", pady=(0, 10))
        
        # 测试按钮
        test_sizes = [
            ("500px", 500, 400),
            ("700px", 700, 500),
            ("900px", 900, 600),
            ("1200px", 1200, 800)
        ]
        
        for text, w, h in test_sizes:
            ttk.Button(
                control_frame,
                text=text,
                command=lambda width=w, height=h: self.test_size(width, height),
                width=8
            ).pack(side="left", padx=2)
        
        ttk.Button(
            control_frame,
            text="重新测试",
            command=self.test_text_width,
            bootstyle="primary"
        ).pack(side="right")
        
        # 信息显示
        self.info_frame = ttk.LabelFrame(main_frame, text="文字宽度分析", padding=10)
        self.info_frame.pack(fill="x", pady=(0, 10))
        
        self.info_text = ttk.Label(
            self.info_frame,
            text="分析中...",
            font=('Consolas', 10),
            foreground='#495057',
            justify='left'
        )
        self.info_text.pack(anchor="w")
        
        # 选项卡容器
        self.notebook_container = ttk.Frame(main_frame)
        self.notebook_container.pack(fill="both", expand=True)
        self.notebook_container.grid_rowconfigure(0, weight=1)
        self.notebook_container.grid_columnconfigure(0, weight=1)
        
        # 创建选项卡
        self.notebook = ttk.Notebook(self.notebook_container)
        self.notebook.grid(row=0, column=0, sticky="nsew")
        
        # 添加选项卡
        self.tab_names = ["单任务", "多任务", "设置"]
        for name in self.tab_names:
            tab = ttk.Frame(self.notebook)
            self.notebook.add(tab, text=name)
            
            ttk.Label(
                tab,
                text=f"{name} 选项卡\n\n检查标题 '{name}' 是否完整显示",
                font=('Microsoft YaHei', 12),
                foreground='#495057',
                justify='center'
            ).pack(expand=True)
    
    def test_size(self, width, height):
        """测试指定窗口大小"""
        self.window.geometry(f"{width}x{height}")
        self.window.after(100, self.test_text_width)
    
    def test_text_width(self):
        """测试文字宽度"""
        try:
            # 更新组件
            self.window.update_idletasks()
            self.notebook_container.update_idletasks()
            self.notebook.update_idletasks()
            
            # 获取尺寸
            window_width = self.window.winfo_width()
            container_width = self.notebook_container.winfo_width()
            tab_count = len(self.tab_names)
            
            if container_width <= 1:
                container_width = window_width - 40
            
            # 计算目标宽度
            target_width = container_width / tab_count
            
            # 测试不同的文字宽度计算方法
            results = []
            
            for font_size in [10, 11, 12]:
                # 创建字体对象来精确测量
                font = tkFont.Font(family='Microsoft YaHei', size=font_size, weight='bold')
                
                actual_widths = []
                for name in self.tab_names:
                    # 使用tkinter的字体测量功能
                    actual_width = font.measure(name)
                    actual_widths.append(actual_width)
                
                max_actual_width = max(actual_widths)
                
                # 不同的估算方法
                char_width_estimates = [13, 14, 15, 16, 17]
                
                for char_width in char_width_estimates:
                    estimated_width = 3 * char_width  # 3个字符
                    error = abs(estimated_width - max_actual_width)
                    error_percent = (error / max_actual_width) * 100
                    
                    results.append({
                        'font_size': font_size,
                        'char_width': char_width,
                        'estimated': estimated_width,
                        'actual': max_actual_width,
                        'error': error,
                        'error_percent': error_percent
                    })
            
            # 找到最佳估算
            best_result = min(results, key=lambda x: x['error_percent'])
            
            # 使用最佳估算配置选项卡
            font_size = best_result['font_size']
            char_width = best_result['char_width']
            text_width = best_result['actual'] + 25  # 加上边距
            
            # 计算padding
            total_padding_needed = target_width - text_width
            padding = max(3, total_padding_needed // 2)
            
            # 应用样式
            self.style.configure(
                'TNotebook.Tab',
                padding=[int(padding), 12],
                font=('Microsoft YaHei', font_size, 'bold'),
                background='#e9ecef',
                foreground='#495057',
                anchor='center'
            )
            
            self.style.map(
                'TNotebook.Tab',
                background=[('selected', '#ffffff'), ('active', '#dee2e6')],
                foreground=[('selected', '#0d6efd'), ('active', '#495057')],
                padding=[('selected', [int(padding), 12]), ('!selected', [int(padding), 12])]
            )
            
            # 强制刷新
            self.notebook.update_idletasks()
            
            # 计算最终结果
            final_width = text_width + padding * 2
            final_percentage = (final_width / container_width * 100) if container_width > 0 else 0
            
            # 更新信息显示
            info_lines = [
                f"窗口尺寸: {window_width}x{self.window.winfo_height()}px",
                f"容器宽度: {container_width}px",
                f"目标宽度: {target_width:.1f}px (33.3%)",
                "",
                f"最佳字体配置:",
                f"  - 字体大小: {font_size}px",
                f"  - 字符宽度估算: {char_width}px",
                f"  - 实际文字宽度: {best_result['actual']}px",
                f"  - 估算误差: {best_result['error_percent']:.1f}%",
                "",
                f"选项卡配置:",
                f"  - 文字宽度(含边距): {text_width}px",
                f"  - Padding: {padding}px × 2 = {padding * 2}px",
                f"  - 最终宽度: {final_width}px ({final_percentage:.1f}%)",
                "",
                f"各选项卡文字实际宽度:"
            ]
            
            # 显示每个选项卡的实际宽度
            font = tkFont.Font(family='Microsoft YaHei', size=font_size, weight='bold')
            for name in self.tab_names:
                actual_width = font.measure(name)
                info_lines.append(f"  - '{name}': {actual_width}px")
            
            # 状态判断
            if final_percentage >= 30 and final_percentage <= 36:
                info_lines.append("\n✅ 1/3分布正常")
            else:
                info_lines.append(f"\n⚠️ 偏离1/3目标: {abs(final_percentage - 33.3):.1f}%")
            
            if padding >= 3:
                info_lines.append("✅ Padding充足，文字应该完整显示")
            else:
                info_lines.append("⚠️ Padding较小，可能有文字遮挡")
            
            self.info_text.config(text="\n".join(info_lines))
            
        except Exception as e:
            print(f"测试出错: {e}")
            import traceback
            traceback.print_exc()
            self.info_text.config(text=f"测试出错: {e}")
    
    def on_resize(self, event):
        """窗口大小变化事件"""
        if event.widget == self.window:
            self.window.after(100, self.test_text_width)
    
    def run(self):
        """运行程序"""
        print("文字宽度测试器启动！")
        print("这个工具会精确测量文字宽度并优化选项卡配置")
        self.window.mainloop()

if __name__ == "__main__":
    app = TextWidthTester()
    app.run()
